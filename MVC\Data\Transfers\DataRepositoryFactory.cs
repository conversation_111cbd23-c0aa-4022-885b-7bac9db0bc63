using Odrc.Dots.Data.Transfers.Interfaces;
using Odrc.Dots.Data.Transfers.RepositoryClasses;

namespace Odrc.Dots.Data.Transfers
{
    public static class DataRepositoryFactory
    {
        private static IMosciRepository? _mosciRepository;

        public static IMosciRepository GetMosciRepository()
        {
            if (_mosciRepository == null)
            {
                _mosciRepository = new MosciRepositoryStub();
            }
            return _mosciRepository;
        }

        public static void SetMosciRepository(IMosciRepository repository)
        {
            _mosciRepository = repository;
        }
    }
}
