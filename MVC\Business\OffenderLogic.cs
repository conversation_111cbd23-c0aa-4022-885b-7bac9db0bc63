using Odrc.Dots.Entities;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Odrc.Dots.Business
{
    public static class OffenderLogic
    {
        private static readonly List<Offender> _mockOffenders = new List<Offender>
        {
            new Offender
            {
                OffenderId = "123456",
                LastName = "SMITH",
                FirstName = "JOHN",
                InstAbbr = "MANCINI",
                InmateIdPrefix = "A",
                InstitutionId = 1
            },
            new Offender
            {
                OffenderId = "789012",
                LastName = "JOHNSON",
                FirstName = "MARY",
                InstAbbr = "BELMONT",
                InmateIdPrefix = "W",
                InstitutionId = 2
            },
            new Offender
            {
                OffenderId = "456789",
                LastName = "BROWN",
                FirstName = "DAVID",
                InstAbbr = "MANCINI",
                InmateIdPrefix = "W",
                InstitutionId = 1
            }
        };

        public static Offender? GetOffender(string combinedOffenderId)
        {
            if (string.IsNullOrWhiteSpace(combinedOffenderId))
            {
                return null;
            }

            // Extract prefix and offender ID
            string prefix = "";
            string offenderId = combinedOffenderId.Trim();

            if (combinedOffenderId.Length > 0 && char.IsLetter(combinedOffenderId[0]))
            {
                prefix = combinedOffenderId[0].ToString().ToUpper();
                offenderId = combinedOffenderId.Substring(1).Trim();
            }

            return _mockOffenders.FirstOrDefault(o => 
                o.OffenderId == offenderId && 
                (string.IsNullOrEmpty(prefix) || o.InmateIdPrefix.Equals(prefix, StringComparison.OrdinalIgnoreCase)));
        }

        public static List<Offender> SearchOffenders(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new List<Offender>();
            }

            return _mockOffenders.Where(o => 
                o.OffenderId.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                o.LastName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                o.FirstName.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }
    }
}
