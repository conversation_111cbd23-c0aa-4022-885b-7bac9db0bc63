using Odrc.Dots.Data.Transfers.Interfaces;
using Odrc.Dots.Entities.Transfers;
using System;
using System.Collections.Generic;

namespace Odrc.Dots.Data.Transfers.RepositoryClasses
{
    public class MosciRepositoryStub : IMosciRepository
    {
        private static readonly List<MosciData> _mockData = new List<MosciData>
        {
            new MosciData
            {
                Oid = "A123456",
                SchdInst = 1,
                Instno = 1,
                Descrl = "Transfer to MANCINI",
                Rowid = Guid.NewGuid().ToString(),
                SchDate = DateTime.Now.AddDays(1),
                SysDate = DateTime.Now,
                LastName = "SMITH",
                FirstName = "JOHN",
                InmateIdPrefix = "A",
                OffenderId = "123456"
            },
            new MosciData
            {
                Oid = "W789012",
                SchdInst = 2,
                Instno = 2,
                Descrl = "Transfer to BELMONT",
                Rowid = Guid.NewGuid().ToString(),
                SchDate = DateTime.Now.AddDays(2),
                SysDate = DateTime.Now,
                LastName = "JOHNSON",
                FirstName = "MARY",
                InmateIdPrefix = "W",
                OffenderId = "789012"
            }
        };

        public List<MosciData> GetMosciInfoByOaksId(string oaksId)
        {
            if (string.IsNullOrWhiteSpace(oaksId))
            {
                return new List<MosciData>();
            }

            var result = new List<MosciData>();
            foreach (var item in _mockData)
            {
                if (item.Oid.Equals(oaksId, StringComparison.OrdinalIgnoreCase))
                {
                    result.Add(item);
                }
            }

            return result;
        }

        public int InsertorUpdateMosci(string combineOffenderId, string SchDate, int Instno, int SchdInst, string Descrl, string username, string rowId)
        {
            // Simulate successful insert/update
            if (string.IsNullOrWhiteSpace(rowId))
            {
                // Insert new record
                var newRecord = new MosciData
                {
                    Oid = combineOffenderId,
                    SchDate = DateTime.TryParse(SchDate, out var parsedDate) ? parsedDate : DateTime.Now,
                    Instno = Instno,
                    SchdInst = SchdInst,
                    Descrl = Descrl,
                    Rowid = Guid.NewGuid().ToString(),
                    SysDate = DateTime.Now
                };
                _mockData.Add(newRecord);
            }
            else
            {
                // Update existing record
                var existingRecord = _mockData.Find(x => x.Rowid == rowId);
                if (existingRecord != null)
                {
                    existingRecord.Oid = combineOffenderId;
                    existingRecord.SchDate = DateTime.TryParse(SchDate, out var parsedDate) ? parsedDate : DateTime.Now;
                    existingRecord.Instno = Instno;
                    existingRecord.SchdInst = SchdInst;
                    existingRecord.Descrl = Descrl;
                }
            }

            return 1; // Simulate successful operation
        }

        public int DeleteMosciRecord(string rowId, string username)
        {
            if (string.IsNullOrWhiteSpace(rowId))
            {
                return 0;
            }

            var recordToRemove = _mockData.Find(x => x.Rowid == rowId);
            if (recordToRemove != null)
            {
                _mockData.Remove(recordToRemove);
                return 1; // Simulate successful deletion
            }

            return 0; // Record not found
        }
    }
}
