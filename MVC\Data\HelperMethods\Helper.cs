using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Configuration;
using System.Collections.Generic;

namespace Odrc.Dots.Data.HelperMethods
{
    public static class Helper
    {
        private static IConfiguration? _configuration;

        public static void Initialize(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public static string ConnectionString
        {
            get
            {
                return _configuration?.GetConnectionString("DefaultConnection") 
                    ?? "Server=(localdb)\\mssqllocaldb;Database=DotsDb;Trusted_Connection=true;MultipleActiveResultSets=true";
            }
        }

        public static List<SelectListItem> GetPrefix(string defaultValue = "0")
        {
            return new List<SelectListItem>
            {
                new SelectListItem { Value = "", Text = "Select Prefix", Selected = defaultValue == "0" },
                new SelectListItem { Value = "A", Text = "A", Selected = defaultValue == "A" },
                new SelectListItem { Value = "W", Text = "W", Selected = defaultValue == "W" },
                new SelectListItem { Value = "M", Text = "M", Selected = defaultValue == "M" },
                new SelectListItem { Value = "F", Text = "F", Selected = defaultValue == "F" }
            };
        }
    }
}
