using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;

namespace Odrdc.Dots.Areas.User.Utilities
{
    public static class HelperMethods
    {
        public static List<SelectListItem> GetInstitutionSelectListByCodeSCHDINSTWithOutAllInsts()
        {
            return new List<SelectListItem>
            {
                new SelectListItem { Value = "0", Text = "Select Institution" },
                new SelectListItem { Value = "1", Text = "MANCINI (CCI)" },
                new SelectListItem { Value = "2", Text = "BELMONT (BCI)" },
                new SelectListItem { Value = "3", Text = "CHILLICOTHE (CRC)" },
                new SelectListItem { Value = "4", Text = "DAYTON (DCI)" },
                new SelectListItem { Value = "5", Text = "GRAFTON (GCI)" }
            };
        }
    }
}
